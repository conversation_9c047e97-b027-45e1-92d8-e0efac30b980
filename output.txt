2025-06-28 08:31:44,335 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-28 08:31:44,335 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-28 08:31:44,336 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-28 08:31:44,336 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-06-28 08:31:44,337 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-06-28 08:31:44,337 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-28 08:31:44,338 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-06-28 08:31:44,338 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-06-28 08:31:44,339 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-06-28 08:31:44,339 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-06-28 08:31:44,339 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-06-28 08:31:44,341 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-06-28 08:31:44,341 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-06-28 08:31:46,405 - __main__ - INFO - Existing processes terminated
2025-06-28 08:31:48,324 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-06-28 08:31:48,385 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-06-28 08:31:49,027 - app - INFO - Using directories from config.py:
2025-06-28 08:31:49,027 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-28 08:31:49,027 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-28 08:31:49,027 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-06-28 08:31:49,033] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-28 08:31:49,033] INFO in database: Test_steps table schema updated successfully
[2025-06-28 08:31:49,033] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 08:31:49,034] INFO in database: Screenshots table schema updated successfully
[2025-06-28 08:31:49,034] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 08:31:49,035] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 08:31:49,035] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 08:31:49,035] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 08:31:49,035] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 08:31:49,035] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 08:31:49,035] INFO in database: Database initialized successfully
[2025-06-28 08:31:49,035] INFO in database: Checking initial database state...
[2025-06-28 08:31:49,056] INFO in database: Database state: 0 suites, 0 cases, 8766 steps, 1 screenshots, 58 tracking entries
[2025-06-28 08:31:49,057] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-28 08:31:49,057] INFO in database: Test_steps table schema updated successfully
[2025-06-28 08:31:49,057] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 08:31:49,058] INFO in database: Screenshots table schema updated successfully
[2025-06-28 08:31:49,058] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 08:31:49,058] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 08:31:49,059] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 08:31:49,059] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 08:31:49,059] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 08:31:49,059] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 08:31:49,059] INFO in database: Database initialized successfully
[2025-06-28 08:31:49,059] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 08:31:49,059] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 08:31:49,060] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 08:31:49,060] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 08:31:49,060] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 08:31:49,060] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 08:31:49,060] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 08:31:49,061] INFO in database: Screenshots table schema updated successfully
[2025-06-28 08:31:49,061] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-06-28 08:31:49,061] INFO in database: Found 58 records in execution_tracking table before clearing
[2025-06-28 08:31:49,063] INFO in database: Successfully cleared execution_tracking table. Removed 58 records.
[2025-06-28 08:31:49,127] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-28 08:31:49,127] INFO in global_values_db: Global values database initialized successfully
[2025-06-28 08:31:49,128] INFO in global_values_db: Using global values from config.py
[2025-06-28 08:31:49,128] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/run.py", line 257, in <module>
    import app as flask_app
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 7135, in <module>
    @app.route('/api/execution/get-data-json', methods=['POST'])
     ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/flask/scaffold.py", line 449, in decorator
    self.add_url_rule(rule, endpoint, f, **options)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/flask/scaffold.py", line 50, in wrapper_func
    return f(self, *args, **kwargs)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/flask/app.py", line 1361, in add_url_rule
    raise AssertionError(
    ...<2 lines>...
    )
AssertionError: View function mapping is overwriting an existing endpoint function: get_execution_data_json
